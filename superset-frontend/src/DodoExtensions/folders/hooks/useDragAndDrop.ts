import { useCallback, useState } from 'react';
import { TreeProps } from 'antd-v5';
import { Entity, TreeNodeData } from '../components/types';

export const useDragAndDrop = (
  data: Entity[],
  onDataChange: (newData: Entity[]) => void,
  onSuccess?: (message: string) => void,
  onError?: (message: string) => void,
) => {
  const [draggedNode, setDraggedNode] = useState<TreeNodeData | null>(null);

  const onDragStart: TreeProps['onDragStart'] = useCallback((info: any) => {
    setDraggedNode(info.node as TreeNodeData);
  }, []);

  const onDragEnd: TreeProps['onDragEnd'] = useCallback(() => {
    setDraggedNode(null);
  }, []);

  const findEntityById = useCallback(
    (entities: Entity[], id: number): Entity | null => {
      for (const entity of entities) {
        if (entity.id === id) {
          return entity;
        }
        if (entity.type === 'folder' && entity.children) {
          const found = findEntityById(entity.children, id);
          if (found) return found;
        }
      }
      return null;
    },
    [],
  );

  const removeEntityById = useCallback(
    (entities: Entity[], id: number): Entity[] =>
      entities
        .filter(entity => entity.id !== id)
        .map(entity => {
          if (entity.type === 'folder' && entity.children) {
            return {
              ...entity,
              children: removeEntityById(entity.children, id),
            };
          }
          return entity;
        }),
    [],
  );

  const addEntityToParent = useCallback(
    (entities: Entity[], entity: Entity, parentId: number | null): Entity[] => {
      if (parentId === null || parentId === 0) {
        // Add to root level
        return [...entities, entity];
      }

      return entities.map(item => {
        if (item.id === parentId && item.type === 'folder') {
          return {
            ...item,
            children: [...(item.children || []), entity],
          };
        }
        if (item.type === 'folder' && item.children) {
          return {
            ...item,
            children: addEntityToParent(item.children, entity, parentId),
          };
        }
        return item;
      });
    },
    [],
  );

  const onDrop: TreeProps['onDrop'] = useCallback(
    async (info: any) => {
      const { dragNode, node, dropToGap } = info;

      if (!draggedNode) return;

      try {
        // Determine target folder
        let targetFolderId: number | null = null;

        if (!dropToGap) {
          // Dropped on a node
          if (node.type === 'folder') {
            targetFolderId = node.id;
          } else {
            // Can't drop on a dashboard, ignore
            return;
          }
        } else {
          // Dropped between nodes
          // Find the parent of the drop position
          const nodeKey = node.key;
          const parentMatch = nodeKey.match(/^(.+)-\d+$/);
          if (parentMatch) {
            const parentKey = parentMatch[1];
            const parentIdMatch = parentKey.match(/folder-(\d+)$/);
            if (parentIdMatch) {
              targetFolderId = parseInt(parentIdMatch[1], 10);
            }
          }
          // If no parent found, it's root level (targetFolderId remains null)
        }

        // Update local state
        const draggedEntity = findEntityById(
          data,
          (dragNode as TreeNodeData).id,
        );
        if (draggedEntity) {
          // Remove from current position
          let newData = removeEntityById(data, (dragNode as TreeNodeData).id);

          // Update parent reference
          const updatedEntity = {
            ...draggedEntity,
            parent: targetFolderId ? targetFolderId.toString() : '0',
          };

          // Add to new position
          newData = addEntityToParent(newData, updatedEntity, targetFolderId);

          onDataChange(newData);
          onSuccess?.('Item moved successfully');
        }
      } catch (error) {
        onError?.('Failed to move item');
      }
    },
    [
      draggedNode,
      data,
      findEntityById,
      removeEntityById,
      addEntityToParent,
      onDataChange,
      onSuccess,
      onError,
    ],
  );

  const allowDrop: TreeProps['allowDrop'] = useCallback(
    ({ dragNode, dropNode, dropPosition }) => {
      const dragNodeData = dragNode as TreeNodeData;
      const dropNodeData = dropNode as TreeNodeData;

      // Don't allow dropping on dashboards
      if (dropNodeData.type === 'dashboard' && dropPosition === 0) {
        return false;
      }

      // Don't allow dropping a folder into itself or its descendants
      if (dragNodeData.type === 'folder' && dropNodeData.type === 'folder') {
        const isDescendant = (parentKey: string, childKey: string): boolean =>
          childKey.startsWith(`${parentKey}-`);

        if (
          dropNodeData.key === dragNodeData.key ||
          isDescendant(dragNodeData.key, dropNodeData.key)
        ) {
          return false;
        }
      }

      return true;
    },
    [],
  );

  return {
    onDragStart,
    onDragEnd,
    onDrop,
    allowDrop,
  };
};
